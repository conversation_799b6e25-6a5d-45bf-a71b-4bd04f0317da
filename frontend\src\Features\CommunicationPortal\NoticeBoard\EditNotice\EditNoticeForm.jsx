import React from "react";
import { Controller } from "react-hook-form";
import { Upload, X } from "lucide-react";
import PriorityDropdown from "../components/PriorityDropdown";
import LabelSelector from "../components/LabelSelector";
import TimePicker from "../components/TimePicker";
import TowerUnitSelector from "../components/TowerUnitSelector";
import PostAsSelector from "../components/PostAsSelector";
import UserCountDisplay from "../components/UserCountDisplay";
import ErrorMessage from "../../../../Components/MessageBox/ErrorMessage";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";

/**
 * EditNoticeForm Component
 * Form component for editing notices
 */
const EditNoticeForm = ({
  // Form props
  control,
  handleSubmit,
  watch,
  setValue,
  errors,
  isSubmitting,
  onSubmit,

  // State props
  currentUser,
  attachments,
  notice,
  hasFormBeenModified,

  // Error states
  fileUploadError,
  dateOrderError,

  // Handlers
  handleFileUpload,
  removeAttachment,
  handleMemberSelect,
  handleGroupSelect,
  isFormValid,

  // Watched values
  watchedValues,
  selectedTowers
}) => {
  return (
    <div className="relative">
      {/* Loading Overlay */}
      {isSubmitting && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50 rounded-lg">
          <LoadingAnimation />
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Notice Images Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-blue-600 mb-4">Notice Images</h3>
          
          <div className="mb-4">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Images <span className="text-blue-600">*</span>
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
                id="edit-notice-file-upload"
              />
              <label
                htmlFor="edit-notice-file-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Upload className="w-8 h-8 text-gray-400 mb-2" />
                <span className="text-sm text-gray-600">Click to upload images</span>
                <span className="text-xs text-gray-500 mt-1">Maximum 10 images, 5MB each</span>
              </label>
            </div>

            {/* Error Message */}
            <ErrorMessage message={fileUploadError} />

            {/* Display uploaded files */}
            {attachments.length > 0 && (
              <div className="mt-3 grid grid-cols-4 gap-2">
                {attachments.map((attachment) => (
                  <div key={attachment.id} className="relative">
                    <img
                      src={attachment.url || attachment.file_url}
                      alt={attachment.name || attachment.file_name}
                      className="w-full h-20 object-cover rounded border"
                    />
                    <button
                      type="button"
                      onClick={() => removeAttachment(attachment.id)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
            
            <p className="mt-2 text-sm text-gray-500">
              {attachments.length} image(s) uploaded
            </p>
          </div>
        </div>

        {/* Notice Settings Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-blue-600 mb-4">Notice Settings</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Priority */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Priority <span className="text-blue-600">*</span>
              </label>
              <Controller
                name="priority"
                control={control}
                render={({ field }) => (
                  <PriorityDropdown
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.priority?.message}
                  />
                )}
              />
              {errors.priority && (
                <p className="mt-1 text-sm text-red-600">{errors.priority.message}</p>
              )}
            </div>

            {/* Label */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Label
              </label>
              <Controller
                name="label"
                control={control}
                render={({ field }) => (
                  <LabelSelector
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.label?.message}
                  />
                )}
              />
            </div>
          </div>
        </div>

        {/* Notice Visibility Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-blue-600 mb-4">Notice Visibility</h3>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* Start Date */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Start Date <span className="text-blue-600">*</span>
              </label>
              <Controller
                name="startDate"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                )}
              />
              {errors.startDate && (
                <p className="mt-1 text-sm text-red-600">{errors.startDate.message}</p>
              )}
            </div>

            {/* Start Time */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Start Time <span className="text-blue-600">*</span>
              </label>
              <Controller
                name="startTime"
                control={control}
                render={({ field }) => (
                  <TimePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select start time"
                  />
                )}
              />
              {errors.startTime && (
                <p className="mt-1 text-sm text-red-600">{errors.startTime.message}</p>
              )}
            </div>

            {/* End Date */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                End Date <span className="text-blue-600">*</span>
              </label>
              <Controller
                name="endDate"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                )}
              />
              {errors.endDate && (
                <p className="mt-1 text-sm text-red-600">{errors.endDate.message}</p>
              )}
            </div>

            {/* End Time */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                End Time <span className="text-blue-600">*</span>
              </label>
              <Controller
                name="endTime"
                control={control}
                render={({ field }) => (
                  <TimePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select end time"
                  />
                )}
              />
              {errors.endTime && (
                <p className="mt-1 text-sm text-red-600">{errors.endTime.message}</p>
              )}
            </div>
          </div>

          {/* Date/Time Validation Error */}
          {dateOrderError && (
            <div className="mt-3">
              <ErrorMessage message={dateOrderError} />
            </div>
          )}
        </div>

        {/* Post As Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-blue-600 mb-4">Post As</h3>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Post As <span className="text-blue-600">*</span>
            </label>
            <Controller
              name="postAs"
              control={control}
              render={({ field }) => (
                <PostAsSelector
                  value={field.value}
                  selectedGroup={watch('postedGroup')}
                  selectedMember={watch('postedMember')}
                  onChange={(postAs, groupId, memberId) => {
                    field.onChange(postAs);
                    setValue('postedGroup', groupId);
                    setValue('postedMember', memberId);
                  }}
                  currentUser={currentUser}
                  error={errors.postAs?.message}
                  disabled={true} // Disable during editing
                />
              )}
            />
            {errors.postAs && (
              <p className="mt-1 text-sm text-red-600">{errors.postAs.message}</p>
            )}
          </div>
        </div>

        {/* Target Audience Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-blue-600 mb-4">Target Audience</h3>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Select Towers & Units <span className="text-blue-600">*</span>
            </label>
            <Controller
              name="targetTowers"
              control={control}
              render={({ field: towerField }) => (
                <Controller
                  name="targetUnits"
                  control={control}
                  render={({ field: unitField }) => (
                    <TowerUnitSelector
                      selectedTowers={towerField.value || []}
                      selectedUnits={unitField.value || []}
                      onChange={(towers, units) => {
                        towerField.onChange(towers);
                        unitField.onChange(units);
                      }}
                      error={errors.targetTowers?.message}
                    />
                  )}
                />
              )}
            />
            {errors.targetTowers && (
              <p className="mt-1 text-sm text-red-600">{errors.targetTowers.message}</p>
            )}
          </div>

          {/* User Count Display */}
          {watchedValues.targetUnits && watchedValues.targetUnits.length > 0 && (
            <div className="mt-4">
              <UserCountDisplay unitIds={watchedValues.targetUnits} />
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-center pt-6 border-t border-gray-200">
          <div className="w-full max-w-md">
            <button
              type="submit"
              disabled={isSubmitting || !isFormValid() || !hasFormBeenModified}
              className={`w-full px-8 py-3 rounded-lg transition-all duration-200 font-semibold ${
                hasFormBeenModified && isFormValid() && !isSubmitting
                  ? 'bg-blue-600 text-white hover:bg-blue-700 cursor-pointer shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
                  : 'bg-gray-200 text-gray-500 cursor-not-allowed'
              } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <LoadingAnimation />
                  <span className="ml-2">Updating Notice...</span>
                </div>
              ) : (
                'Update Notice'
              )}
            </button>
            
            <div className="mt-2 text-center">
              <span className="text-xs text-gray-500">
                <span className="text-blue-600">*</span> Required fields
              </span>
            </div>
            
            {!hasFormBeenModified && (
              <p className="mt-2 text-xs text-gray-500 text-center">
                Make changes to enable the update button
              </p>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default EditNoticeForm;
