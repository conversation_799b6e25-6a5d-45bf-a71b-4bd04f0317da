import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ArrowLeft } from 'lucide-react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import NoticePreview from '../components/NoticePreview';
import EditNoticeForm from './EditNoticeForm';
import MessageBox from '../../../../Components/MessageBox/MessageBox';
import LoadingAnimation from '../../../../Components/Loaders/LoadingAnimation';
import { useCurrentUser } from '../hooks/useCurrentUser';
import { useNotices } from "../../../../hooks/useNotices";

// Validation schema
const noticeSchema = yup.object().shape({
  priority: yup.string().required('Priority is required'),
  label: yup.string(),
  startDate: yup.string().required('Start date is required'),
  startTime: yup.string().required('Start time is required'),
  endDate: yup.string().required('End date is required'),
  endTime: yup.string().required('End time is required'),
  postAs: yup.string().required('Post as selection is required'),
  postedGroup: yup.number().when('postAs', {
    is: 'group',
    then: (schema) => schema.required('Group selection is required when posting as group'),
    otherwise: (schema) => schema.nullable()
  }),
  postedMember: yup.number().when('postAs', {
    is: 'member',
    then: (schema) => schema.required('Member selection is required when posting as member'),
    otherwise: (schema) => schema.nullable()
  }),
  targetTowers: yup.array().min(1, "At least one tower must be selected"),
  targetUnits: yup.array(),
  attachments: yup.array().min(1, "At least one image is required")
});

/**
 * EditNotice Component
 * Main component for editing existing notices with real-time preview
 */
const EditNotice = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams();
  const { currentUser } = useCurrentUser();
  const isMountedRef = useRef(true);
  const initialLoadRef = useRef(false);
  
  // Local state
  const [attachments, setAttachments] = useState([]);
  const [attachmentsToDelete, setAttachmentsToDelete] = useState([]);
  const [loading, setLoading] = useState(true);
  const [successMessage, setSuccessMessage] = useState('');
  const [fileUploadError, setFileUploadError] = useState('');
  const [error, setError] = useState(null);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [dateOrderError, setDateOrderError] = useState("");
  const [hasFormBeenModified, setHasFormBeenModified] = useState(false);
  const [originalFormData, setOriginalFormData] = useState(null);
  const [isFormInitialized, setIsFormInitialized] = useState(false);

  // Get the source tab from location state
  const sourceTab = location.state?.activeTab || 1;

  // Redux hooks for notice editing
  const {
    selectedNotice,
    loading: noticeLoading,
    updating,
    updateError,
    updateSuccess,
    message,
    loadNotice,
    updateNotice: updateNoticeRedux,
    clearAllSuccess,
    clearErrors
  } = useNotices();

  // Form setup with validation
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    reset,
    formState: { errors, isSubmitting }
  } = useForm({
    resolver: yupResolver(noticeSchema),
    mode: 'onChange',
    defaultValues: {
      priority: 'normal',
      label: '',
      startDate: '',
      startTime: '',
      endDate: '',
      endTime: '',
      postAs: 'creator',
      postedGroup: null,
      postedMember: null,
      targetTowers: [],
      targetUnits: [],
      attachments: []
    }
  });

  // Stable function references
  const clearAllState = useCallback(() => {
    clearAllSuccess();
    clearErrors();
    setSuccessMessage('');
    setError(null);
    setHasSubmitted(false);
  }, [clearAllSuccess, clearErrors]);

  const resetLocalState = useCallback(() => {
    setAttachments([]);
    setAttachmentsToDelete([]);
    setIsFormInitialized(false);
    setHasFormBeenModified(false);
    setOriginalFormData(null);
    setFileUploadError('');
    setDateOrderError('');
  }, []);

  // Check if all required fields are filled
  const isFormValid = useCallback(() => {
    const values = getValues();
    return (
      values.priority &&
      values.startDate &&
      values.startTime &&
      values.endDate &&
      values.endTime &&
      values.targetTowers?.length > 0 &&
      attachments.length > 0 &&
      (values.postAs === 'creator' ||
        (values.postAs === 'group' && values.postedGroup) ||
        (values.postAs === 'member' && values.postedMember))
    );
  }, [getValues, attachments.length]);

  // Watch form values for preview
  const watchedValues = watch();
  const selectedTowers = watch('targetTowers');

  // Initialize component state only once
  useEffect(() => {
    if (!initialLoadRef.current) {
      initialLoadRef.current = true;
      clearAllState();
      resetLocalState();
    }
  }, []);

  // Load notice data when component mounts or ID changes
  useEffect(() => {
    if (id && !selectedNotice && !noticeLoading) {
      console.log('Loading notice with ID:', id);
      setLoading(true);
      loadNotice(id).catch((error) => {
        console.error('Error loading notice:', error);
        if (isMountedRef.current) {
          setError('Failed to load notice. Please try again.');
          setLoading(false);
        }
      });
    }
  }, [id, selectedNotice, noticeLoading, loadNotice]);

  // Handle notice data when loaded - only run once when data arrives
  useEffect(() => {
    if (selectedNotice && !isFormInitialized && isMountedRef.current) {
      console.log('Initializing form with notice data:', selectedNotice);
      setLoading(false);

      try {
        // Set attachments
        if (selectedNotice.attachments && selectedNotice.attachments.length > 0) {
          const existingAttachments = selectedNotice.attachments.map((att, index) => ({
            id: att.id || index,
            url: att.file_url,
            name: att.file_name,
            type: att.file_type || 'image/*',
            isExisting: true
          }));
          setAttachments(existingAttachments);
        }

        // Reset form with notice data
        const formData = {
          priority: selectedNotice.priority || 'normal',
          label: selectedNotice.label || '',
          startDate: selectedNotice.start_date || '',
          startTime: selectedNotice.start_time || '',
          endDate: selectedNotice.end_date || '',
          endTime: selectedNotice.end_time || '',
          postAs: selectedNotice.post_as || 'creator',
          postedGroup: selectedNotice.posted_group || null,
          postedMember: selectedNotice.posted_member || null,
          targetTowers: selectedNotice.target_towers_data?.map(tower => Number(tower.id)) || [],
          targetUnits: selectedNotice.target_units_data?.map(unit => Number(unit.id)) || [],
          attachments: selectedNotice.attachments || []
        };

        reset(formData);

        // Store original form data for comparison
        setOriginalFormData({
          ...formData,
          attachments: selectedNotice.attachments || []
        });

        setIsFormInitialized(true);
      } catch (error) {
        console.error('Error processing notice data:', error);
        setError('Failed to process notice data. Please try again.');
      }
    }
  }, [selectedNotice, isFormInitialized, reset]);

  // Watch for form changes to determine if form has been modified
  useEffect(() => {
    if (originalFormData && isFormInitialized) {
      const currentValues = getValues();
      
      const hasChanged =
        currentValues.priority !== originalFormData.priority ||
        currentValues.label !== originalFormData.label ||
        currentValues.startDate !== originalFormData.startDate ||
        currentValues.startTime !== originalFormData.startTime ||
        currentValues.endDate !== originalFormData.endDate ||
        currentValues.endTime !== originalFormData.endTime ||
        currentValues.postAs !== originalFormData.postAs ||
        currentValues.postedGroup !== originalFormData.postedGroup ||
        currentValues.postedMember !== originalFormData.postedMember ||
        JSON.stringify((currentValues.targetTowers || []).sort()) !== JSON.stringify((originalFormData.targetTowers || []).sort()) ||
        JSON.stringify((currentValues.targetUnits || []).sort()) !== JSON.stringify((originalFormData.targetUnits || []).sort()) ||
        attachments.length !== originalFormData.attachments.length ||
        attachmentsToDelete.length > 0;

      setHasFormBeenModified(hasChanged);
    }
  }, [watchedValues, originalFormData, isFormInitialized, attachments.length, attachmentsToDelete.length, getValues]);

  // Handle date/time validation
  useEffect(() => {
    const startDate = watchedValues.startDate;
    const startTime = watchedValues.startTime;
    const endDate = watchedValues.endDate;
    const endTime = watchedValues.endTime;

    setDateOrderError('');

    if (startDate && startTime && endDate && endTime) {
      const start = new Date(`${startDate}T${startTime}`);
      const end = new Date(`${endDate}T${endTime}`);

      if (start >= end) {
        setDateOrderError('End date/time must be after start date/time');
      }
    }
  }, [watchedValues.startDate, watchedValues.startTime, watchedValues.endDate, watchedValues.endTime]);

  // Handle loading and error states
  useEffect(() => {
    if (isMountedRef.current) {
      setLoading(noticeLoading);
    }
  }, [noticeLoading]);

  useEffect(() => {
    if (updateError && isMountedRef.current) {
      console.error('Update error:', updateError);
      setError(updateError);
      setHasSubmitted(false);
    }
  }, [updateError]);

  // Handle success message
  useEffect(() => {
    if (updateSuccess && message && hasSubmitted && !updating && isMountedRef.current) {
      setSuccessMessage(message);
      setHasSubmitted(false);
      setHasFormBeenModified(false);
    }
  }, [updateSuccess, message, hasSubmitted, updating]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Handle group selection
  const handleGroupSelect = useCallback((groupData) => {
    if (groupData) {
      setValue('postedGroup', groupData.id);
      setValue('postedGroupName', groupData.name);
    } else {
      setValue('postedGroup', null);
      setValue('postedGroupName', '');
    }
  }, [setValue]);

  // Handle member selection
  const handleMemberSelect = useCallback((memberData) => {
    if (memberData) {
      setValue('postedMember', memberData.id);
      setValue('postedMemberName', memberData.name);
    } else {
      setValue('postedMember', null);
      setValue('postedMemberName', '');
    }
  }, [setValue]);

  // Utility function to convert file to base64
  const fileToBase64 = useCallback((file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  }, []);

  // Handle file upload
  const handleFileUpload = useCallback(async (event) => {
    if (!event.target.files || event.target.files.length === 0) {
      return;
    }

    const files = Array.from(event.target.files);

    // Check file limit
    if (attachments.length + files.length > 10) {
      setFileUploadError('Please upload maximum 10 files to proceed.');
      event.target.value = '';
      return;
    }

    // Validate files
    const validFiles = [];
    for (const file of files) {
      const isImage = file.type.startsWith('image/');
      
      if (!isImage) {
        setFileUploadError('Only images are allowed.');
        event.target.value = '';
        return;
      }

      if (file.size > 5 * 1024 * 1024) {
        setFileUploadError(`File "${file.name}" exceeds 5MB limit.`);
        event.target.value = '';
        return;
      }

      validFiles.push(file);
    }

    setFileUploadError('');

    try {
      const newAttachments = await Promise.all(
        validFiles.map(async (file) => {
          const base64 = await fileToBase64(file);
          return {
            id: Date.now() + Math.random(),
            file,
            url: base64,
            base64: base64,
            name: file.name,
            type: file.type
          };
        })
      );

      setAttachments(prev => [...prev, ...newAttachments]);
    } catch (error) {
      console.error('Error processing files:', error);
      setFileUploadError('Error processing files. Please try again.');
    }

    event.target.value = '';
  }, [attachments.length, fileToBase64]);

  // Remove attachment
  const removeAttachment = useCallback((id) => {
    const attachmentToRemove = attachments.find(att => att.id === id);

    if (attachmentToRemove && attachmentToRemove.isExisting) {
      setAttachmentsToDelete(prev => [...prev, id]);
    }

    setAttachments(prev => prev.filter(att => att.id !== id));
  }, [attachments]);

  // Handle form submission
  const onSubmit = useCallback(async (data) => {
    setDateOrderError("");
    const start = new Date(`${data.startDate}T${data.startTime}`);
    const end = new Date(`${data.endDate}T${data.endTime}`);
    
    if (start > end) {
      setDateOrderError("Start date/time must be before end date/time");
      return;
    }

    try {
      setError(null);
      setHasSubmitted(true);

      const formData = new FormData();

      // Add basic fields
      formData.append('priority', data.priority);
      if (data.label) formData.append('label', data.label);
      formData.append('start_date', data.startDate);
      formData.append('start_time', data.startTime);
      formData.append('end_date', data.endDate);
      formData.append('end_time', data.endTime);
      formData.append('post_as', data.postAs);

      // Add post as specific fields
      if (data.postAs === 'group' && data.postedGroup) {
        formData.append('posted_group', data.postedGroup);
      }
      if (data.postAs === 'member' && data.postedMember) {
        formData.append('posted_member', data.postedMember);
      }

      // Add target towers and units
      (data.targetTowers || []).forEach(towerId => {
        formData.append('target_tower_ids', towerId);
      });
      (data.targetUnits || []).forEach(unitId => {
        formData.append('target_unit_ids', unitId);
      });

      // Add attachments to delete
      attachmentsToDelete.forEach(id => formData.append('existing_attachment_ids_to_delete', id));

      // Add new attachments
      const newAttachments = attachments.filter(att => !att.isExisting);
      newAttachments.forEach((att) => {
        if (att.file) {
          formData.append('attachments', att.file);
        }
      });

      // Add existing attachment IDs to keep
      const existingAttachments = attachments.filter(att => att.isExisting);
      existingAttachments.forEach(attachment => {
        formData.append('existing_attachment_ids', attachment.id);
      });

      // Make the API call
      await updateNoticeRedux({ id: parseInt(id), data: formData });
    } catch (error) {
      console.error('Error updating notice:', error);
      setHasSubmitted(false);
      setError('An unexpected error occurred. Please try again.');
    }
  }, [id, attachments, attachmentsToDelete, updateNoticeRedux]);

  // Handle back navigation
  const handleBack = useCallback(() => {
    clearAllState();
    navigate('/communication-portal/notice-board', {
      state: { activeTab: sourceTab },
      replace: true
    });
  }, [clearAllState, navigate, sourceTab]);

  // Clear success message
  const clearMessage = useCallback(() => {
    setSuccessMessage('');
  }, []);

  // Handle success message OK button
  const handleSuccessOk = useCallback(() => {
    clearAllState();
    navigate('/communication-portal/notice-board', {
      state: { activeTab: sourceTab },
      replace: true
    });
  }, [clearAllState, navigate, sourceTab]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingAnimation />
      </div>
    );
  }

  // Show error state
  if (error) {
    const errorMessage = typeof error === 'object' ? error.message : error;

    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">{errorMessage}</div>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Notice Board
          </button>
        </div>
      </div>
    );
  }

  // Don't render form until it's initialized
  if (!isFormInitialized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingAnimation />
      </div>
    );
  }

  // Prepare data for preview component
  const previewData = {
    priority: watchedValues.priority,
    label: watchedValues.label,
    startDate: watchedValues.startDate,
    startTime: watchedValues.startTime,
    endDate: watchedValues.endDate,
    endTime: watchedValues.endTime,
    postAs: watchedValues.postAs,
    selectedUnits: watchedValues.targetUnits,
    attachments: attachments.map(att => ({
      preview: att.base64 || att.url,
      url: att.url || att.base64,
      base64: att.base64,
      name: att.name,
      type: att.type
    }))
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <button
              onClick={handleBack}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              <span className="text-lg font-semibold">Edit Notice</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Left Column - Preview */}
          <div className="order-2 lg:order-1 lg:col-span-4">
            <div className="bg-white rounded-lg shadow-sm p-6 lg:sticky lg:top-8 h-screen lg:h-[calc(100vh-6rem)] overflow-y-auto">
              <NoticePreview data={previewData} currentUser={currentUser} />
            </div>
          </div>

          {/* Right Column - Form */}
          <div className="order-1 lg:order-2 lg:col-span-8 bg-white rounded-lg shadow-sm p-6">
            <EditNoticeForm
              control={control}
              handleSubmit={handleSubmit}
              watch={watch}
              setValue={setValue}
              errors={errors}
              isSubmitting={isSubmitting}
              onSubmit={onSubmit}
              currentUser={currentUser}
              attachments={attachments}
              notice={selectedNotice}
              hasFormBeenModified={hasFormBeenModified}
              fileUploadError={fileUploadError}
              dateOrderError={dateOrderError}
              handleFileUpload={handleFileUpload}
              removeAttachment={removeAttachment}
              handleMemberSelect={handleMemberSelect}
              handleGroupSelect={handleGroupSelect}
              isFormValid={isFormValid}
              watchedValues={watchedValues}
              selectedTowers={selectedTowers}
            />
          </div>
        </div>
      </div>

      {/* Success Message Box */}
      {successMessage && (
        <MessageBox
          message={successMessage}
          clearMessage={clearMessage}
          onOk={handleSuccessOk}
        />
      )}
    </div>
  );
};

export default EditNotice;
